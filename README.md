# تك إنوفيشن - موقع الشركة التقنية

موقع ويب احترافي وحديث لشركة تقنية متقدمة باللغة العربية مع دعم كامل للـ RTL.

## المميزات

- **تصميم RTL كامل**: دعم كامل للغة العربية واتجاه النص من اليمين إلى اليسار
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة والشاشات
- **رسوم متحركة متقدمة**: استخدام مكتبة GSAP للحصول على تأثيرات بصرية مذهلة
- **أداء محسن**: كود نظيف ومحسن للسرعة
- **تصميم حديث**: واجهة مستخدم عصرية وجذابة

## التقنيات المستخدمة

- **HTML5**: هيكل الموقع الأساسي
- **CSS3**: التصميم والتنسيق مع دعم RTL
- **Bootstrap 5.3**: إطار العمل للتصميم المتجاوب
- **JavaScript ES6+**: البرمجة التفاعلية
- **GSAP**: مكتبة الرسوم المتحركة المتقدمة
- **Font Awesome**: الأيقونات
- **IBM Plex Sans Arabic**: الخط العربي

## الألوان المستخدمة

- **اللون الأساسي**: `#0077B6` (أزرق تقني)
- **اللون الثانوي**: `#00B4D8` (أزرق سماوي)
- **لون التمييز**: `#48CAE4` (سيان ناعم)
- **الخلفية**: `#F8F9FA` (رمادي فاتح)
- **النص**: `#212529` (أسود غني)
- **التمييز**: `#F9A826` (برتقالي/عنبر)

## هيكل الموقع

### الصفحة الرئيسية
- **قسم البطل**: مقدمة مع رسوم متحركة للجسيمات
- **قسم من نحن**: معلومات عن الشركة
- **قسم الخدمات**: عرض الخدمات المقدمة
- **قسم لماذا نحن**: إحصائيات وأرقام
- **قسم المشاريع**: عرض المشاريع المميزة
- **قسم آراء العملاء**: تقييمات العملاء
- **قسم دعوة للعمل**: حث المستخدمين على التواصل
- **قسم المدونة**: أحدث المقالات
- **التذييل**: معلومات التواصل والروابط

### الصفحات الإضافية (قيد التطوير)
- من نحن
- الخدمات
- المشاريع
- المدونة
- الوظائف
- تواصل معنا

## الملفات والمجلدات

```
├── index.html              # الصفحة الرئيسية
├── css/
│   └── style.css          # ملف التنسيق الرئيسي
├── js/
│   └── main.js            # ملف JavaScript الرئيسي
├── images/
│   ├── logo.svg           # شعار الشركة
│   └── logo-white.svg     # شعار الشركة (أبيض)
└── README.md              # هذا الملف
```

## كيفية الاستخدام

1. **تحميل الملفات**: قم بتحميل جميع الملفات إلى خادم الويب
2. **تخصيص المحتوى**: قم بتعديل النصوص والصور حسب احتياجاتك
3. **إضافة الصور**: أضف الصور المطلوبة في مجلد `images/`
4. **اختبار الموقع**: تأكد من عمل جميع الميزات بشكل صحيح

## الصور المطلوبة

يحتاج الموقع إلى الصور التالية في مجلد `images/`:

- `about-team.jpg` - صورة فريق العمل
- `project-1.jpg` - صورة المشروع الأول
- `project-2.jpg` - صورة المشروع الثاني
- `project-3.jpg` - صورة المشروع الثالث
- `client-1.jpg` - صورة العميل الأول
- `client-2.jpg` - صورة العميل الثاني
- `client-3.jpg` - صورة العميل الثالث
- `blog-1.jpg` - صورة المقال الأول
- `blog-2.jpg` - صورة المقال الثاني
- `blog-3.jpg` - صورة المقال الثالث

## المميزات التقنية

### الرسوم المتحركة
- رسوم متحركة للنصوص عند التحميل
- تأثيرات التمرير (Scroll Animations)
- رسوم متحركة للجسيمات في الخلفية
- تأثيرات الحوم (Hover Effects)
- عدادات متحركة للإحصائيات

### التفاعل
- قائمة تنقل ذكية
- تمرير سلس بين الأقسام
- نماذج تفاعلية
- أزرار متحركة

### الأداء
- تحميل محسن للموارد
- كود CSS و JavaScript محسن
- صور محسنة للويب
- دعم للأجهزة المحمولة

## الدعم والتطوير

هذا الموقع قابل للتخصيص والتطوير. يمكن إضافة المزيد من الصفحات والميزات حسب الحاجة.

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله حسب الحاجة.
