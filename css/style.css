/* ===== CSS Variables ===== */
:root {
    --primary-color: #0077B6;
    --secondary-color: #00B4D8;
    --accent-color: #48CAE4;
    --background-color: #F8F9FA;
    --text-color: #212529;
    --highlight-color: #F9A826;
    --white: #FFFFFF;
    --light-gray: #E9ECEF;
    --dark-gray: #6C757D;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* ===== Global Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'IBM Plex Sans Arabic', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    direction: rtl;
    text-align: right;
}

html {
    scroll-behavior: smooth;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--dark-gray);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== Header & Navigation ===== */
.header-main {
    position: relative;
    z-index: 1000;
}

.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 119, 182, 0.1);
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow);
}

.navbar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo {
    height: 40px;
    margin-left: 10px;
}

.brand-text {
    color: var(--primary-color);
}

.navbar-nav .nav-link {
    color: var(--text-color);
    font-weight: 500;
    margin: 0 1rem;
    padding: 0.5rem 0;
    position: relative;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

/* ===== Hero Section ===== */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

#particles-canvas {
    width: 100%;
    height: 100%;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.1;
}

.hero-line-1,
.hero-line-2 {
    display: block;
    opacity: 0;
    transform: translateY(50px);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0;
    transform: translateY(30px);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    opacity: 0;
    transform: translateY(30px);
}

.btn {
    padding: 12px 30px;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--highlight-color);
    border-color: var(--highlight-color);
    color: var(--white);
}

.btn-primary:hover {
    background: transparent;
    border-color: var(--highlight-color);
    color: var(--highlight-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.btn-outline-primary {
    background: transparent;
    border-color: var(--white);
    color: var(--white);
}

.btn-outline-primary:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    right: 50%;
    transform: translateX(50%);
    z-index: 2;
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid var(--white);
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: rotate(-45deg) translateY(0);
    }
    40% {
        transform: rotate(-45deg) translateY(-10px);
    }
    60% {
        transform: rotate(-45deg) translateY(-5px);
    }
}

/* ===== About Section ===== */
.about-section {
    background: var(--white);
    padding: 100px 0;
}

.about-content {
    padding-left: 2rem;
}

.about-text {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    color: var(--dark-gray);
}

.about-features {
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature-item i {
    color: var(--primary-color);
    margin-left: 1rem;
    font-size: 1.2rem;
}

.about-image {
    padding-right: 2rem;
}

.about-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* ===== Services Section ===== */
.services-section {
    background: var(--background-color);
    padding: 100px 0;
}

.service-card {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--light-gray);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: var(--transition);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-icon i {
    font-size: 2rem;
    color: var(--white);
}

.service-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.service-description {
    color: var(--dark-gray);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.service-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.service-link:hover {
    color: var(--secondary-color);
}

.service-link i {
    margin-right: 0.5rem;
    transition: var(--transition);
}

.service-link:hover i {
    transform: translateX(-5px);
}

/* ===== Why Choose Us Section ===== */
.why-section {
    background: var(--white);
    padding: 100px 0;
}

.stat-card {
    padding: 2rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--dark-gray);
    font-weight: 500;
}

/* ===== Projects Section ===== */
.projects-section {
    background: var(--background-color);
    padding: 100px 0;
}

.project-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow);
    height: 100%;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 119, 182, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-info {
    text-align: center;
    color: var(--white);
    padding: 2rem;
}

.project-info h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.project-content {
    padding: 2rem;
}

.project-content h5 {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.project-content p {
    color: var(--dark-gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: var(--accent-color);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* ===== Testimonials Section ===== */
.testimonials-section {
    background: var(--white);
    padding: 100px 0;
}

.testimonial-card {
    background: var(--background-color);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    height: 100%;
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.quote-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--dark-gray);
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    margin-left: 1rem;
    border: 3px solid var(--primary-color);
}

.author-info h5 {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 0.3rem;
}

.author-info span {
    font-size: 0.9rem;
    color: var(--dark-gray);
}

.testimonial-rating {
    color: var(--highlight-color);
}

.testimonial-rating i {
    margin: 0 0.2rem;
}

/* ===== Call to Action Section ===== */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.cta-section .container {
    position: relative;
    z-index: 2;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.cta-subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    line-height: 1.6;
}

.cta-buttons .btn {
    margin: 0.5rem;
}

/* ===== Blog Section ===== */
.blog-section {
    background: var(--background-color);
    padding: 100px 0;
}

.blog-card {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow);
    height: 100%;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.blog-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-category {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary-color);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.blog-content {
    padding: 2rem;
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--dark-gray);
}

.blog-title {
    font-size: 1.3rem;
    color: var(--text-color);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.blog-excerpt {
    color: var(--dark-gray);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.blog-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
}

.blog-link:hover {
    color: var(--secondary-color);
}

.blog-link i {
    margin-right: 0.5rem;
    transition: var(--transition);
}

.blog-link:hover i {
    transform: translateX(-3px);
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .about-content,
    .about-image {
        padding: 0;
        margin-bottom: 2rem;
    }

    .service-card,
    .project-card,
    .testimonial-card,
    .blog-card {
        margin-bottom: 2rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .testimonial-author {
        flex-direction: column;
        text-align: center;
    }

    .author-image {
        margin-left: 0;
        margin-bottom: 1rem;
    }

    .blog-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .hero-buttons .btn,
    .cta-buttons .btn {
        display: block;
        margin-bottom: 1rem;
        margin-left: 0;
        margin-right: 0;
    }

    .project-tags,
    .testimonial-rating {
        justify-content: center;
    }
}

/* ===== Footer Section ===== */
.footer-section {
    background: var(--text-color);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-widget {
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.footer-logo .logo {
    height: 40px;
    margin-left: 10px;
    filter: brightness(0) invert(1);
}

.footer-logo .brand-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
}

.footer-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--white);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.8rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
    padding-right: 5px;
}

.contact-info {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
    color: var(--primary-color);
    margin-left: 1rem;
    width: 20px;
}

.newsletter h6 {
    color: var(--white);
    margin-bottom: 1rem;
}

.newsletter-form .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.newsletter-form .form-control {
    border: none;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: 0;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: none;
    color: var(--white);
}

.newsletter-form .btn {
    border-radius: 0;
    padding: 12px 20px;
    border: none;
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 3rem 0 2rem;
}

.copyright {
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0;
}

.footer-bottom-links {
    text-align: left;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    margin-left: 2rem;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .footer-bottom-links {
        text-align: center;
        margin-top: 1rem;
    }

    .footer-bottom-links a {
        display: block;
        margin: 0.5rem 0;
    }

    .social-links {
        justify-content: center;
    }
}

/* ===== Contact Page Styles ===== */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    padding: 150px 0 100px;
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.contact-section {
    background: var(--background-color);
    padding: 100px 0;
}

.contact-form-wrapper {
    background: var(--white);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.contact-form .form-group {
    margin-bottom: 1.5rem;
}

.contact-form .form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.contact-form .form-control {
    padding: 12px 15px;
    border: 2px solid var(--light-gray);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 119, 182, 0.25);
}

.contact-form textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

.contact-info {
    background: var(--white);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    height: fit-content;
}

.contact-info-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--light-gray);
}

.contact-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-left: 1rem;
    flex-shrink: 0;
}

.contact-details h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--dark-gray);
    margin-bottom: 0;
    line-height: 1.6;
}

.social-links-contact {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--light-gray);
}

.social-links-contact h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.map-section {
    height: 400px;
    position: relative;
}

.map-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--light-gray) 25%, transparent 25%),
                linear-gradient(-45deg, var(--light-gray) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, var(--light-gray) 75%),
                linear-gradient(-45deg, transparent 75%, var(--light-gray) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-overlay {
    background: rgba(0, 119, 182, 0.9);
    color: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.map-info h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.map-info p {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .contact-form-wrapper,
    .contact-info {
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        margin: 0 auto 1rem;
    }

    .map-section {
        height: 300px;
    }
}
